/* Mathematical Mind Game Styles */

:root {
  font-family: 'Se<PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Color scheme */
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;

  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

  --radius: 8px;
  --radius-lg: 12px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

#app {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Authentication Styles */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

.auth-card {
  background: var(--bg-secondary);
  padding: 3rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.auth-card h2 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.auth-card h3 {
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-weight: 500;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(79 70 229 / 0.1);
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.auth-switch {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.auth-switch a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.auth-switch a:hover {
  text-decoration: underline;
}

/* Message Styles */
.message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: var(--radius);
  font-weight: 500;
  text-align: center;
}

.message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.message.info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* Game Dashboard Styles */
.game-container {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  min-height: 80vh;
}

.game-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.user-info h2 {
  margin-bottom: 0.5rem;
  font-size: 1.75rem;
}

.user-id {
  font-size: 0.875rem;
  opacity: 0.9;
}

.highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.dashboard {
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.stats-panel, .game-panel {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.stats-panel h3, .game-panel h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.game-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.game-panel p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.125rem;
}

.game-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.game-note {
  font-size: 0.875rem !important;
  color: var(--text-secondary) !important;
  margin-bottom: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .game-header {
    flex-direction: column;
    text-align: center;
  }

  .dashboard {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .user-info h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 1.5rem;
  }

  .stats-grid {
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

/* Game-Specific Styles */

/* Game Lobby */
.game-lobby {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.game-rules {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.game-rules h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.game-rules ul {
  list-style: none;
  padding: 0;
}

.game-rules li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.game-rules li:last-child {
  border-bottom: none;
}

.positive {
  color: var(--success-color);
  font-weight: bold;
}

.negative {
  color: var(--error-color);
  font-weight: bold;
}

.lobby-status {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.game-mode-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.game-mode-card {
  background: white;
  padding: 2rem;
  border-radius: var(--radius);
  border: 2px solid var(--border-color);
  text-align: center;
  transition: all 0.2s;
}

.game-mode-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.game-mode-card h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.game-mode-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.waiting-info {
  display: flex;
  justify-content: space-around;
  margin: 2rem 0;
}

.players-waiting, .players-needed {
  text-align: center;
}

.count {
  display: block;
  font-size: 3rem;
  font-weight: bold;
  color: var(--primary-color);
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.queue-status {
  margin-top: 1rem;
  min-height: 2rem;
}

/* Game Board */
.game-board {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  padding: 2rem;
}

.players-panel {
  background: var(--bg-primary);
  padding: 1.5rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.player-card {
  background: white;
  padding: 1rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  border: 2px solid transparent;
  transition: all 0.2s;
}

.player-card.current-player {
  border-color: var(--primary-color);
  background: #f0f9ff;
}

.player-card.eliminated {
  opacity: 0.5;
  background: #fef2f2;
}

.player-name {
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.player-score {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.player-status {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Choice Panel */
.choice-panel {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.number-input-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.number-input-section input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius);
  font-size: 1.125rem;
  text-align: center;
}

.number-input-section input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.quick-numbers h4 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.number-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
}

.quick-number {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  background: white;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.quick-number:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.quick-number:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.eliminated-message {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

/* Timer */
.timer-display {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.timer-display .urgent {
  color: var(--error-color);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Round Results */
.round-results {
  margin-top: 2rem;
}

.round-result-card {
  background: var(--bg-secondary);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius);
  padding: 2rem;
  margin-bottom: 1rem;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calculation-display {
  margin-bottom: 2rem;
}

.choices {
  margin-bottom: 1rem;
}

.choice-item {
  display: inline-block;
  background: var(--bg-primary);
  padding: 0.5rem 1rem;
  margin: 0.25rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.calculation {
  background: #f0f9ff;
  padding: 1rem;
  border-radius: var(--radius);
  margin: 1rem 0;
}

.calc-step {
  font-size: 1.125rem;
  margin: 0.5rem 0;
  font-family: 'Courier New', monospace;
}

.winner-announcement {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  padding: 1.5rem;
  border-radius: var(--radius);
  text-align: center;
}

.winner-announcement h4 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.scores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}

.score-item.eliminated {
  background: #fef2f2;
  opacity: 0.7;
}

.score-item .name {
  font-weight: 500;
}

.score-item .score {
  font-weight: bold;
  font-size: 1.125rem;
}

.score-item .status {
  font-size: 0.875rem;
  color: var(--error-color);
  font-weight: bold;
}

/* Game End Screen */
.game-end-screen {
  text-align: center;
  padding: 3rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 800px;
  margin: 2rem auto;
}

.winner-celebration {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 2rem;
  border-radius: var(--radius);
  margin-bottom: 2rem;
}

.winner-celebration h3 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.final-scores {
  margin: 2rem 0;
}

.scores-table {
  background: var(--bg-primary);
  border-radius: var(--radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.score-row {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  align-items: center;
}

.score-row:last-child {
  border-bottom: none;
}

.score-row.winner {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  font-weight: bold;
}

.score-row .rank {
  font-weight: bold;
  color: var(--primary-color);
}

.score-row .name {
  text-align: left;
}

.score-row .score {
  font-weight: bold;
}

.score-row .status {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.game-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Responsive Game Styles */
@media (max-width: 768px) {
  .game-lobby {
    grid-template-columns: 1fr;
  }

  .game-mode-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .game-board {
    grid-template-columns: 1fr;
  }

  .number-input-section {
    flex-direction: column;
  }

  .number-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .scores-grid {
    grid-template-columns: 1fr;
  }

  .game-actions {
    flex-direction: column;
  }

  .score-row {
    grid-template-columns: auto 1fr auto;
    gap: 0.5rem;
  }

  .score-row .status {
    grid-column: 2 / -1;
    text-align: left;
    margin-top: 0.25rem;
  }
}

/* Demo Mode Styles */
.demo-lobby {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.demo-info {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.ai-players {
  margin: 2rem 0;
}

.ai-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ai-player {
  background: white;
  padding: 0.75rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.game-rules-summary {
  margin-top: 2rem;
}

.game-rules-summary ul {
  list-style: none;
  padding: 0;
}

.game-rules-summary li {
  padding: 0.25rem 0;
  font-size: 0.875rem;
}

.demo-actions {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.demo-note {
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.player-card.ai-player {
  border-left: 4px solid var(--warning-color);
}

.player-card.ai-player .player-name {
  color: var(--warning-color);
}

/* Demo responsive */
@media (max-width: 768px) {
  .demo-lobby {
    grid-template-columns: 1fr;
  }

  .game-options {
    gap: 0.75rem;
  }
}
