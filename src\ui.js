// UI Components for Authentication and Game Interface

export class AuthUI {
  constructor(userManager) {
    this.userManager = userManager;
    this.currentView = 'login';
  }

  // Create login form
  createLoginForm() {
    return `
      <div class="auth-container">
        <div class="auth-card">
          <h2>🧮 Mathematical Mind Game</h2>
          <h3>Login to Your Account</h3>
          
          <form id="loginForm" class="auth-form">
            <div class="form-group">
              <label for="loginUsername">Username or Email:</label>
              <input type="text" id="loginUsername" required>
            </div>
            
            <div class="form-group">
              <label for="loginPassword">Password:</label>
              <input type="password" id="loginPassword" required>
            </div>
            
            <button type="submit" class="btn btn-primary">Login</button>
          </form>
          
          <div class="auth-switch">
            <p>Don't have an account? 
              <a href="#" id="showRegister">Register here</a>
            </p>
          </div>
          
          <div id="loginMessage" class="message"></div>
        </div>
      </div>
    `;
  }

  // Create registration form
  createRegistrationForm() {
    return `
      <div class="auth-container">
        <div class="auth-card">
          <h2>🧮 Mathematical Mind Game</h2>
          <h3>Create New Account</h3>
          
          <form id="registerForm" class="auth-form">
            <div class="form-group">
              <label for="registerUsername">Username:</label>
              <input type="text" id="registerUsername" required minlength="3">
              <small>At least 3 characters</small>
            </div>
            
            <div class="form-group">
              <label for="registerEmail">Email:</label>
              <input type="email" id="registerEmail" required>
            </div>
            
            <div class="form-group">
              <label for="registerPassword">Password:</label>
              <input type="password" id="registerPassword" required minlength="6">
              <small>At least 6 characters</small>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword">Confirm Password:</label>
              <input type="password" id="confirmPassword" required>
            </div>
            
            <button type="submit" class="btn btn-primary">Register</button>
          </form>
          
          <div class="auth-switch">
            <p>Already have an account? 
              <a href="#" id="showLogin">Login here</a>
            </p>
          </div>
          
          <div id="registerMessage" class="message"></div>
        </div>
      </div>
    `;
  }

  // Create game dashboard
  createGameDashboard(user) {
    return `
      <div class="game-container">
        <header class="game-header">
          <div class="user-info">
            <h2>Welcome, ${user.username}! 🎯</h2>
            <div class="user-id">Your ID: <span class="highlight">${user.id}</span></div>
          </div>
          <button id="logoutBtn" class="btn btn-secondary">Logout</button>
        </header>
        
        <div class="dashboard">
          <div class="stats-panel">
            <h3>Your Game Statistics</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">${user.gameStats.gamesPlayed}</div>
                <div class="stat-label">Games Played</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${user.gameStats.totalScore}</div>
                <div class="stat-label">Total Score</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${user.gameStats.bestScore}</div>
                <div class="stat-label">Best Score</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${user.gameStats.level}</div>
                <div class="stat-label">Current Level</div>
              </div>
            </div>
          </div>
          
          <div class="game-panel">
            <h3>Ready to Play?</h3>
            <p>Your mathematical mind game adventure awaits!</p>
            <div class="game-options">
              <button id="startGameBtn" class="btn btn-primary btn-large">Find 5-Player Game</button>
              <button id="startDemoBtn" class="btn btn-secondary btn-large">Play Demo vs AI</button>
            </div>
            <p class="game-note">Demo mode lets you play against 4 AI opponents to learn the game!</p>
          </div>
        </div>
      </div>
    `;
  }

  // Show message to user
  showMessage(elementId, message, type = 'info') {
    const messageEl = document.getElementById(elementId);
    if (messageEl) {
      messageEl.textContent = message;
      messageEl.className = `message ${type}`;
      
      // Clear message after 5 seconds
      setTimeout(() => {
        messageEl.textContent = '';
        messageEl.className = 'message';
      }, 5000);
    }
  }

  // Setup event listeners for authentication
  setupAuthListeners() {
    // Login form submission
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
      loginForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleLogin();
      });
    }

    // Registration form submission
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
      registerForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleRegistration();
      });
    }

    // Switch between login and register
    const showRegister = document.getElementById('showRegister');
    if (showRegister) {
      showRegister.addEventListener('click', (e) => {
        e.preventDefault();
        this.showRegistration();
      });
    }

    const showLogin = document.getElementById('showLogin');
    if (showLogin) {
      showLogin.addEventListener('click', (e) => {
        e.preventDefault();
        this.showLogin();
      });
    }

    // Logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        this.handleLogout();
      });
    }

    // Start game button
    const startGameBtn = document.getElementById('startGameBtn');
    if (startGameBtn) {
      startGameBtn.addEventListener('click', () => {
        this.startGame();
      });
    }

    // Start demo button
    const startDemoBtn = document.getElementById('startDemoBtn');
    if (startDemoBtn) {
      startDemoBtn.addEventListener('click', () => {
        this.startDemo();
      });
    }
  }

  // Handle login
  handleLogin() {
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;

    try {
      const result = this.userManager.login(username, password);
      this.showMessage('loginMessage', result.message, 'success');
      
      // Redirect to dashboard after short delay
      setTimeout(() => {
        this.showDashboard();
      }, 1000);
      
    } catch (error) {
      this.showMessage('loginMessage', error.message, 'error');
    }
  }

  // Handle registration
  handleRegistration() {
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (password !== confirmPassword) {
      this.showMessage('registerMessage', 'Passwords do not match', 'error');
      return;
    }

    try {
      const result = this.userManager.register(username, email, password);
      this.showMessage('registerMessage', result.message, 'success');
      
      // Auto-login after registration
      setTimeout(() => {
        this.userManager.login(username, password);
        this.showDashboard();
      }, 1500);
      
    } catch (error) {
      this.showMessage('registerMessage', error.message, 'error');
    }
  }

  // Handle logout
  handleLogout() {
    this.userManager.logout();
    this.showLogin();
  }

  // Show login view
  showLogin() {
    this.currentView = 'login';
    document.getElementById('app').innerHTML = this.createLoginForm();
    this.setupAuthListeners();
  }

  // Show registration view
  showRegistration() {
    this.currentView = 'register';
    document.getElementById('app').innerHTML = this.createRegistrationForm();
    this.setupAuthListeners();
  }

  // Show dashboard
  showDashboard() {
    const user = this.userManager.getCurrentUser();
    if (user) {
      document.getElementById('app').innerHTML = this.createGameDashboard(user);
      this.setupAuthListeners();
    }
  }

  // Start game
  startGame() {
    // Import and initialize game UI
    import('./gameUI.js').then(({ GameUI }) => {
      import('./game.js').then(({ gameRoomManager }) => {
        const gameUI = new GameUI(this.userManager, gameRoomManager);
        gameUI.showGameLobby();
      });
    });
  }

  // Start demo game
  startDemo() {
    // Import and initialize demo UI
    import('./demo.js').then(({ GameDemo, DemoGameUI }) => {
      const gameDemo = new GameDemo();
      const demoUI = new DemoGameUI(this.userManager, gameDemo);
      demoUI.showDemoLobby();
    });
  }

  // Initialize the UI
  init() {
    if (this.userManager.isLoggedIn()) {
      this.showDashboard();
    } else {
      this.showLogin();
    }
  }
}
