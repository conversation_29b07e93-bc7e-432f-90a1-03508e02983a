// Demo system for testing the mathematical mind game

import { MathMindGame } from './game.js';

export class GameDemo {
  constructor() {
    this.game = null;
    this.demoPlayers = [
      { id: 'DEMO_1', username: '<PERSON>' },
      { id: 'DEMO_2', username: '<PERSON>' },
      { id: 'DEMO_3', username: '<PERSON>' },
      { id: 'DEMO_4', username: '<PERSON>' }
    ];
  }

  // Create a demo game with the current user + 4 AI players
  createDemoGame(currentUser) {
    this.game = new MathMindGame();
    
    // Add the current user
    this.game.addPlayer(currentUser);
    
    // Add 4 demo players
    this.demoPlayers.forEach(player => {
      this.game.addPlayer(player);
    });

    return this.game;
  }

  // Start the demo game
  startDemo() {
    if (!this.game) {
      throw new Error('No demo game created');
    }

    const result = this.game.startGame();
    
    // Set up AI player automation
    this.setupAIPlayers();
    
    return result;
  }

  // Setup AI players to make automatic choices
  setupAIPlayers() {
    // Listen for round starts and make AI choices
    const makeAIChoices = () => {
      if (this.game.gameState !== 'playing') return;

      // Wait a random time (1-10 seconds) then make choices for AI players
      setTimeout(() => {
        this.demoPlayers.forEach(player => {
          if (!this.game.getPlayer(player.id)?.isEliminated) {
            try {
              // AI strategy: choose numbers based on different strategies
              const choice = this.getAIChoice(player.id);
              this.game.makeChoice(player.id, choice);
            } catch (error) {
              // Player might have already chosen or been eliminated
              console.log(`AI ${player.username} couldn't make choice:`, error.message);
            }
          }
        });
      }, Math.random() * 10000 + 1000); // 1-11 seconds
    };

    // Start AI choices for first round
    makeAIChoices();

    // Set up interval to check for new rounds
    this.aiInterval = setInterval(() => {
      if (this.game.gameState === 'playing') {
        makeAIChoices();
      } else {
        clearInterval(this.aiInterval);
      }
    }, 2000);
  }

  // AI choice strategies
  getAIChoice(playerId) {
    const strategies = {
      'DEMO_1': () => Math.floor(Math.random() * 30) + 35, // Alice: 35-65 (middle range)
      'DEMO_2': () => Math.floor(Math.random() * 20) + 1,  // Bob: 1-20 (low numbers)
      'DEMO_3': () => Math.floor(Math.random() * 20) + 80, // Charlie: 80-100 (high numbers)
      'DEMO_4': () => Math.floor(Math.random() * 100) + 1  // Diana: 1-100 (random)
    };

    return strategies[playerId] ? strategies[playerId]() : Math.floor(Math.random() * 100) + 1;
  }

  // Get the current game
  getGame() {
    return this.game;
  }

  // Clean up demo
  cleanup() {
    if (this.aiInterval) {
      clearInterval(this.aiInterval);
    }
  }
}

// Enhanced GameUI for demo mode
export class DemoGameUI {
  constructor(userManager, gameDemo) {
    this.userManager = userManager;
    this.gameDemo = gameDemo;
    this.game = null;
    this.updateInterval = null;
  }

  // Create demo game lobby
  createDemoLobby() {
    const user = this.userManager.getCurrentUser();
    
    return `
      <div class="game-container">
        <header class="game-header">
          <div class="user-info">
            <h2>Mathematical Mind Game - Demo Mode 🧮</h2>
            <div class="user-id">Player: ${user.username} (ID: ${user.id})</div>
          </div>
          <button id="backToDashboard" class="btn btn-secondary">Back to Dashboard</button>
        </header>
        
        <div class="demo-lobby">
          <div class="demo-info">
            <h3>🎮 Demo Mode</h3>
            <p>Play against 4 AI opponents to test the game mechanics!</p>
            
            <div class="ai-players">
              <h4>Your AI Opponents:</h4>
              <div class="ai-list">
                <div class="ai-player">🤖 Alice - Plays middle numbers (35-65)</div>
                <div class="ai-player">🤖 Bob - Plays low numbers (1-20)</div>
                <div class="ai-player">🤖 Charlie - Plays high numbers (80-100)</div>
                <div class="ai-player">🤖 Diana - Plays random numbers (1-100)</div>
              </div>
            </div>
            
            <div class="game-rules-summary">
              <h4>Quick Rules Reminder:</h4>
              <ul>
                <li>Choose a number 1-100 each round</li>
                <li>All 5 numbers are summed and multiplied by 0.8</li>
                <li>Closest player to the result gets +1 point</li>
                <li>Others get -1 point</li>
                <li>Reach -10 points and you're eliminated!</li>
                <li>Last player standing wins!</li>
              </ul>
            </div>
          </div>
          
          <div class="demo-actions">
            <button id="startDemo" class="btn btn-primary btn-large">Start Demo Game</button>
            <p class="demo-note">AI players will make choices automatically</p>
          </div>
        </div>
      </div>
    `;
  }

  // Show demo game interface
  showDemoGame() {
    const gameState = this.game.getGameState();
    const user = this.userManager.getCurrentUser();
    const currentPlayer = gameState.players.find(p => p.id === user.id);
    
    return `
      <div class="game-container">
        <header class="game-header">
          <div class="game-info">
            <h2>Demo Game - Round ${gameState.currentRound} 🎯</h2>
            <div class="timer-display">
              <span id="timeRemaining">30</span>s
            </div>
          </div>
          <button id="endDemo" class="btn btn-secondary">End Demo</button>
        </header>
        
        <div class="game-board">
          <div class="players-panel">
            <h3>Players</h3>
            <div class="players-list">
              ${gameState.players.map(player => `
                <div class="player-card ${player.isEliminated ? 'eliminated' : ''} ${player.id === user.id ? 'current-player' : ''} ${player.id.startsWith('DEMO_') ? 'ai-player' : ''}">
                  <div class="player-name">
                    ${player.id.startsWith('DEMO_') ? '🤖 ' : '👤 '}${player.username}
                  </div>
                  <div class="player-score ${player.score >= 0 ? 'positive' : 'negative'}">
                    ${player.score} pts
                  </div>
                  <div class="player-status">
                    ${player.isEliminated ? '❌ Eliminated' : 
                      player.hasChosenThisRound ? '✅ Ready' : '⏳ Choosing...'}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="choice-panel">
            <h3>Choose Your Number (1-100)</h3>
            
            ${currentPlayer && !currentPlayer.isEliminated ? `
              <div class="number-input-section">
                <input type="number" id="numberChoice" min="1" max="100" 
                       placeholder="Enter 1-100" ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>
                <button id="submitChoice" class="btn btn-primary" 
                        ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>
                  ${currentPlayer.hasChosenThisRound ? 'Choice Submitted' : 'Submit Choice'}
                </button>
              </div>
              
              <div class="quick-numbers">
                <h4>Quick Select:</h4>
                <div class="number-grid">
                  ${[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map(num => `
                    <button class="quick-number ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}" 
                            data-number="${num}" ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>${num}</button>
                  `).join('')}
                </div>
              </div>
            ` : `
              <div class="eliminated-message">
                <h4>You have been eliminated</h4>
                <p>Watch the AI players compete!</p>
              </div>
            `}
          </div>
        </div>
        
        <div id="roundResults" class="round-results"></div>
      </div>
    `;
  }

  // Setup demo event listeners
  setupDemoListeners() {
    // Start demo button
    const startDemoBtn = document.getElementById('startDemo');
    if (startDemoBtn) {
      startDemoBtn.addEventListener('click', () => {
        this.startDemo();
      });
    }

    // Submit choice button
    const submitChoiceBtn = document.getElementById('submitChoice');
    if (submitChoiceBtn) {
      submitChoiceBtn.addEventListener('click', () => {
        this.submitChoice();
      });
    }

    // Quick number selection
    const quickNumbers = document.querySelectorAll('.quick-number');
    quickNumbers.forEach(btn => {
      btn.addEventListener('click', () => {
        if (!btn.disabled) {
          const number = parseInt(btn.dataset.number);
          document.getElementById('numberChoice').value = number;
          this.submitChoice();
        }
      });
    });

    // Number input enter key
    const numberInput = document.getElementById('numberChoice');
    if (numberInput) {
      numberInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.submitChoice();
        }
      });
    }

    // Navigation buttons
    const backBtn = document.getElementById('backToDashboard');
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        this.backToDashboard();
      });
    }

    const endDemoBtn = document.getElementById('endDemo');
    if (endDemoBtn) {
      endDemoBtn.addEventListener('click', () => {
        this.endDemo();
      });
    }
  }

  // Start the demo
  startDemo() {
    const user = this.userManager.getCurrentUser();
    this.game = this.gameDemo.createDemoGame(user);
    this.gameDemo.startDemo();
    
    // Show game interface
    document.getElementById('app').innerHTML = this.showDemoGame();
    this.setupDemoListeners();
    
    // Start updating the interface
    this.startUpdating();
  }

  // Submit player choice
  submitChoice() {
    const numberInput = document.getElementById('numberChoice');
    const number = parseInt(numberInput.value);
    
    if (!number || number < 1 || number > 100) {
      alert('Please enter a number between 1 and 100');
      return;
    }

    try {
      const user = this.userManager.getCurrentUser();
      this.game.makeChoice(user.id, number);
      
      // Update UI
      this.updateGameInterface();
      
    } catch (error) {
      alert(error.message);
    }
  }

  // Update game interface periodically
  startUpdating() {
    this.updateInterval = setInterval(() => {
      this.updateGameInterface();
    }, 1000);
  }

  updateGameInterface() {
    if (this.game.gameState === 'finished') {
      this.showGameEnd();
      return;
    }

    // Update the game display
    document.getElementById('app').innerHTML = this.showDemoGame();
    this.setupDemoListeners();
    
    // Show latest round result if available
    const lastResult = this.game.gameHistory[this.game.gameHistory.length - 1];
    if (lastResult) {
      this.showRoundResult(lastResult);
    }
  }

  showRoundResult(roundResult) {
    const resultsDiv = document.getElementById('roundResults');
    if (resultsDiv) {
      resultsDiv.innerHTML = this.createRoundResults(roundResult);
    }
  }

  createRoundResults(roundResult) {
    return `
      <div class="round-result-card">
        <h3>Round ${roundResult.round} Results</h3>
        
        <div class="calculation-display">
          <div class="choices">
            <h4>Player Choices:</h4>
            ${roundResult.choices.map(choice => `
              <span class="choice-item">${choice.username}: ${choice.choice}</span>
            `).join('')}
          </div>
          
          <div class="calculation">
            <div class="calc-step">Sum: ${roundResult.sum}</div>
            <div class="calc-step">Target: ${roundResult.sum} × 0.8 = ${roundResult.target.toFixed(2)}</div>
          </div>
          
          <div class="winner-announcement">
            <h4>🏆 Round Winner: ${roundResult.winner.username}</h4>
            <p>Chose ${roundResult.winner.choice} (Distance: ${roundResult.minDistance.toFixed(2)})</p>
          </div>
        </div>
      </div>
    `;
  }

  showGameEnd() {
    clearInterval(this.updateInterval);
    const gameResult = this.game.endGame();
    
    document.getElementById('app').innerHTML = `
      <div class="game-container">
        <div class="game-end-screen">
          <h2>🎉 Demo Game Complete!</h2>
          
          ${gameResult.winner ? `
            <div class="winner-celebration">
              <h3>🏆 Winner: ${gameResult.winner.username}</h3>
              <p>${gameResult.winner.id.startsWith('DEMO_') ? 'AI Player' : 'You'} won after ${gameResult.totalRounds} rounds!</p>
            </div>
          ` : `
            <div class="no-winner">
              <h3>Game Ended</h3>
              <p>Demo completed</p>
            </div>
          `}
          
          <div class="final-scores">
            <h3>Final Scores</h3>
            <div class="scores-table">
              ${gameResult.finalScores
                .sort((a, b) => b.score - a.score)
                .map((player, index) => `
                  <div class="score-row ${index === 0 && !player.isEliminated ? 'winner' : ''}">
                    <span class="rank">#${index + 1}</span>
                    <span class="name">${player.id.startsWith('DEMO_') ? '🤖 ' : '👤 '}${player.username}</span>
                    <span class="score ${player.score >= 0 ? 'positive' : 'negative'}">${player.score}</span>
                    <span class="status">${player.isEliminated ? 'Eliminated' : 'Survived'}</span>
                  </div>
                `).join('')}
            </div>
          </div>
          
          <div class="game-actions">
            <button id="playAgainDemo" class="btn btn-primary">Play Demo Again</button>
            <button id="backToDashboard" class="btn btn-secondary">Back to Dashboard</button>
          </div>
        </div>
      </div>
    `;
    
    // Setup end game listeners
    document.getElementById('playAgainDemo')?.addEventListener('click', () => {
      this.showDemoLobby();
    });
    
    document.getElementById('backToDashboard')?.addEventListener('click', () => {
      this.backToDashboard();
    });
  }

  showDemoLobby() {
    document.getElementById('app').innerHTML = this.createDemoLobby();
    this.setupDemoListeners();
  }

  endDemo() {
    this.gameDemo.cleanup();
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    this.backToDashboard();
  }

  backToDashboard() {
    this.gameDemo.cleanup();
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    window.location.reload();
  }
}
