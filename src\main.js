import './style.css'
import { userManager } from './auth.js'
import { AuthUI } from './ui.js'

// Initialize the application
class MathMindGame {
  constructor() {
    this.userManager = userManager;
    this.authUI = new AuthUI(this.userManager);
  }

  // Initialize the game
  init() {
    console.log('🧮 Mathematical Mind Game - Starting...');

    // Initialize the authentication UI
    this.authUI.init();

    console.log('✅ Game initialized successfully!');
  }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const game = new MathMindGame();
  game.init();
});

// Also start immediately if DOM is already loaded
if (document.readyState === 'loading') {
  // DOM is still loading
} else {
  // DOM is already loaded
  const game = new MathMindGame();
  game.init();
}
