// Mathematical Mind Game - Core Game Logic

export class MathMindGame {
  constructor() {
    this.gameState = 'waiting'; // waiting, playing, finished
    this.players = [];
    this.currentRound = 0;
    this.roundTimer = null;
    this.roundTimeLimit = 30; // 30 seconds per round
    this.gameHistory = [];
    this.maxPlayers = 5;
    this.eliminationScore = -10;
  }

  // Player management
  addPlayer(user) {
    if (this.players.length >= this.maxPlayers) {
      throw new Error('Game is full (5 players maximum)');
    }

    if (this.gameState !== 'waiting') {
      throw new Error('Cannot join game in progress');
    }

    const player = {
      id: user.id,
      username: user.username,
      score: 0,
      isEliminated: false,
      currentChoice: null,
      hasChosenThisRound: false,
      joinedAt: new Date().toISOString()
    };

    this.players.push(player);
    return player;
  }

  removePlayer(playerId) {
    this.players = this.players.filter(p => p.id !== playerId);
    
    // If we have less than 2 players, end the game
    if (this.getActivePlayers().length < 2 && this.gameState === 'playing') {
      this.endGame();
    }
  }

  getPlayer(playerId) {
    return this.players.find(p => p.id === playerId);
  }

  getActivePlayers() {
    return this.players.filter(p => !p.isEliminated);
  }

  // Game state management
  canStartGame() {
    return this.players.length === this.maxPlayers && this.gameState === 'waiting';
  }

  startGame() {
    if (!this.canStartGame()) {
      throw new Error('Cannot start game - need exactly 5 players');
    }

    this.gameState = 'playing';
    this.currentRound = 1;
    this.startNewRound();
    
    return {
      success: true,
      message: 'Game started!',
      gameState: this.getGameState()
    };
  }

  startNewRound() {
    // Reset player choices for new round
    this.players.forEach(player => {
      player.currentChoice = null;
      player.hasChosenThisRound = false;
    });

    // Start round timer
    this.startRoundTimer();
    
    return {
      round: this.currentRound,
      timeLimit: this.roundTimeLimit,
      activePlayers: this.getActivePlayers().length
    };
  }

  startRoundTimer() {
    if (this.roundTimer) {
      clearTimeout(this.roundTimer);
    }

    this.roundTimer = setTimeout(() => {
      this.processRoundResults();
    }, this.roundTimeLimit * 1000);
  }

  // Player choice handling
  makeChoice(playerId, number) {
    const player = this.getPlayer(playerId);
    
    if (!player) {
      throw new Error('Player not found');
    }

    if (player.isEliminated) {
      throw new Error('Eliminated players cannot make choices');
    }

    if (this.gameState !== 'playing') {
      throw new Error('Game is not in progress');
    }

    if (number < 1 || number > 100 || !Number.isInteger(number)) {
      throw new Error('Number must be an integer between 1 and 100');
    }

    player.currentChoice = number;
    player.hasChosenThisRound = true;

    // Check if all active players have made their choices
    const activePlayers = this.getActivePlayers();
    const playersWhoChose = activePlayers.filter(p => p.hasChosenThisRound);
    
    if (playersWhoChose.length === activePlayers.length) {
      // All players have chosen, process results immediately
      clearTimeout(this.roundTimer);
      this.processRoundResults();
    }

    return {
      success: true,
      choice: number,
      playersReady: playersWhoChose.length,
      totalActivePlayers: activePlayers.length
    };
  }

  // Round processing
  processRoundResults() {
    const activePlayers = this.getActivePlayers();
    
    // Get all choices (use random number for players who didn't choose)
    const choices = activePlayers.map(player => {
      if (!player.hasChosenThisRound) {
        // Auto-assign random number for players who didn't choose in time
        player.currentChoice = Math.floor(Math.random() * 100) + 1;
        player.hasChosenThisRound = true;
      }
      return {
        playerId: player.id,
        username: player.username,
        choice: player.currentChoice
      };
    });

    // Calculate the target number
    const sum = choices.reduce((total, choice) => total + choice.choice, 0);
    const target = sum * 0.8;

    // Find the winner (closest to target)
    let winner = null;
    let minDistance = Infinity;

    choices.forEach(choice => {
      const distance = Math.abs(choice.choice - target);
      if (distance < minDistance) {
        minDistance = distance;
        winner = choice;
      }
    });

    // Update scores
    activePlayers.forEach(player => {
      if (player.id === winner.playerId) {
        player.score += 1; // Winner gets +1
      } else {
        player.score -= 1; // Others get -1
      }

      // Check for elimination
      if (player.score <= this.eliminationScore) {
        player.isEliminated = true;
      }
    });

    // Create round result
    const roundResult = {
      round: this.currentRound,
      choices: choices,
      sum: sum,
      target: target,
      winner: winner,
      minDistance: minDistance,
      playerScores: activePlayers.map(p => ({
        id: p.id,
        username: p.username,
        score: p.score,
        isEliminated: p.isEliminated
      })),
      timestamp: new Date().toISOString()
    };

    this.gameHistory.push(roundResult);

    // Check game end conditions
    const remainingPlayers = this.getActivePlayers();
    if (remainingPlayers.length <= 1) {
      this.endGame();
      return roundResult;
    }

    // Prepare for next round
    this.currentRound++;
    setTimeout(() => {
      this.startNewRound();
    }, 5000); // 5 second break between rounds

    return roundResult;
  }

  endGame() {
    this.gameState = 'finished';
    
    if (this.roundTimer) {
      clearTimeout(this.roundTimer);
      this.roundTimer = null;
    }

    const remainingPlayers = this.getActivePlayers();
    const gameWinner = remainingPlayers.length === 1 ? remainingPlayers[0] : null;

    const gameResult = {
      winner: gameWinner,
      totalRounds: this.currentRound,
      finalScores: this.players.map(p => ({
        id: p.id,
        username: p.username,
        score: p.score,
        isEliminated: p.isEliminated
      })),
      gameHistory: this.gameHistory,
      endedAt: new Date().toISOString()
    };

    return gameResult;
  }

  // Game state for UI
  getGameState() {
    return {
      gameState: this.gameState,
      currentRound: this.currentRound,
      players: this.players.map(p => ({
        id: p.id,
        username: p.username,
        score: p.score,
        isEliminated: p.isEliminated,
        hasChosenThisRound: p.hasChosenThisRound
      })),
      activePlayers: this.getActivePlayers().length,
      maxPlayers: this.maxPlayers,
      canStart: this.canStartGame(),
      lastRoundResult: this.gameHistory[this.gameHistory.length - 1] || null
    };
  }

  // Get time remaining in current round
  getRemainingTime() {
    // This would need to be implemented with actual timing logic
    // For now, return a placeholder
    return this.roundTimeLimit;
  }
}

// Game Room Manager for handling multiple game instances
export class GameRoomManager {
  constructor() {
    this.rooms = new Map();
    this.waitingPlayers = [];
  }

  // Add player to waiting queue
  addPlayerToQueue(user) {
    // Remove player from any existing queue
    this.removePlayerFromQueue(user.id);
    
    this.waitingPlayers.push({
      id: user.id,
      username: user.username,
      joinedQueue: new Date().toISOString()
    });

    // Try to create a game if we have enough players
    this.tryCreateGame();

    return {
      position: this.waitingPlayers.length,
      playersNeeded: 5 - this.waitingPlayers.length
    };
  }

  removePlayerFromQueue(playerId) {
    this.waitingPlayers = this.waitingPlayers.filter(p => p.id !== playerId);
  }

  tryCreateGame() {
    if (this.waitingPlayers.length >= 5) {
      const gameId = this.generateGameId();
      const game = new MathMindGame();
      
      // Add first 5 players to the game
      const selectedPlayers = this.waitingPlayers.splice(0, 5);
      selectedPlayers.forEach(player => {
        game.addPlayer(player);
      });

      this.rooms.set(gameId, game);
      
      return {
        gameId: gameId,
        game: game,
        players: selectedPlayers
      };
    }
    return null;
  }

  generateGameId() {
    return `GAME_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  getGame(gameId) {
    return this.rooms.get(gameId);
  }

  removeGame(gameId) {
    this.rooms.delete(gameId);
  }

  getWaitingPlayersCount() {
    return this.waitingPlayers.length;
  }
}

// Global game room manager instance
export const gameRoomManager = new GameRoomManager();
