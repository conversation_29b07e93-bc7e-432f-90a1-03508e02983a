// User Authentication and Management System

class UserManager {
  constructor() {
    this.currentUser = null;
    this.users = this.loadUsers();
  }

  // Load users from localStorage
  loadUsers() {
    const stored = localStorage.getItem('mathGameUsers');
    return stored ? JSON.parse(stored) : {};
  }

  // Save users to localStorage
  saveUsers() {
    localStorage.setItem('mathGameUsers', JSON.stringify(this.users));
  }

  // Generate unique user ID
  generateUserId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `USER_${timestamp}_${random}`;
  }

  // Validate email format
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Validate password strength
  isValidPassword(password) {
    return password.length >= 6;
  }

  // Register new user
  register(username, email, password) {
    // Validation
    if (!username || username.length < 3) {
      throw new Error('Username must be at least 3 characters long');
    }

    if (!this.isValidEmail(email)) {
      throw new Error('Please enter a valid email address');
    }

    if (!this.isValidPassword(password)) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Check if username or email already exists
    const existingUser = Object.values(this.users).find(
      user => user.username === username || user.email === email
    );

    if (existingUser) {
      throw new Error('Username or email already exists');
    }

    // Create new user
    const userId = this.generateUserId();
    const newUser = {
      id: userId,
      username,
      email,
      password: this.hashPassword(password), // Simple hash for demo
      createdAt: new Date().toISOString(),
      gameStats: {
        gamesPlayed: 0,
        totalScore: 0,
        bestScore: 0,
        level: 1
      }
    };

    this.users[userId] = newUser;
    this.saveUsers();

    return {
      success: true,
      message: 'Registration successful!',
      userId: userId
    };
  }

  // Simple password hashing (for demo purposes - use proper hashing in production)
  hashPassword(password) {
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Login user
  login(usernameOrEmail, password) {
    const hashedPassword = this.hashPassword(password);
    
    const user = Object.values(this.users).find(
      user => (user.username === usernameOrEmail || user.email === usernameOrEmail) 
              && user.password === hashedPassword
    );

    if (!user) {
      throw new Error('Invalid username/email or password');
    }

    this.currentUser = user;
    localStorage.setItem('currentUser', JSON.stringify(user));

    return {
      success: true,
      message: 'Login successful!',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        gameStats: user.gameStats
      }
    };
  }

  // Logout user
  logout() {
    this.currentUser = null;
    localStorage.removeItem('currentUser');
  }

  // Check if user is logged in
  isLoggedIn() {
    if (this.currentUser) return true;
    
    const stored = localStorage.getItem('currentUser');
    if (stored) {
      this.currentUser = JSON.parse(stored);
      return true;
    }
    
    return false;
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Update user game stats
  updateGameStats(score, level) {
    if (!this.currentUser) return;

    const user = this.users[this.currentUser.id];
    if (user) {
      user.gameStats.gamesPlayed++;
      user.gameStats.totalScore += score;
      user.gameStats.bestScore = Math.max(user.gameStats.bestScore, score);
      user.gameStats.level = Math.max(user.gameStats.level, level);
      
      this.currentUser = user;
      this.saveUsers();
      localStorage.setItem('currentUser', JSON.stringify(user));
    }
  }
}

// Create global instance
export const userManager = new UserManager();
