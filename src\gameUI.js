// Game UI Components for Mathematical Mind Game

export class GameUI {
  constructor(userManager, gameRoomManager) {
    this.userManager = userManager;
    this.gameRoomManager = gameRoomManager;
    this.currentGame = null;
    this.currentGameId = null;
    this.selectedNumber = null;
    this.timerInterval = null;
    this.remainingTime = 0;
    this.aiPlayers = [
      { id: 'AI_1', username: '<PERSON>' },
      { id: 'AI_2', username: '<PERSON>' },
      { id: 'AI_3', username: '<PERSON>' },
      { id: 'AI_4', username: '<PERSON>' }
    ];
    this.aiInterval = null;
  }

  // Create game lobby interface
  createGameLobby() {
    const user = this.userManager.getCurrentUser();
    const waitingCount = this.gameRoomManager.getWaitingPlayersCount();
    
    return `
      <div class="game-container">
        <header class="game-header">
          <div class="user-info">
            <h2>Mathematical Mind Game 🧮</h2>
            <div class="user-id">Player: ${user.username} (ID: ${user.id})</div>
          </div>
          <button id="backToDashboard" class="btn btn-secondary">Back to Dashboard</button>
        </header>
        
        <div class="game-lobby">
          <div class="game-rules">
            <h3>🎯 Game Rules</h3>
            <ul>
              <li><strong>5 Players</strong> compete in infinite rounds</li>
              <li>Each round: Choose a number from <strong>1 to 100</strong></li>
              <li><strong>Calculation:</strong> Sum all 5 numbers → multiply by 0.8</li>
              <li><strong>Winner:</strong> Player closest to result gets <span class="positive">+1 point</span></li>
              <li><strong>Others:</strong> Get <span class="negative">-1 point</span></li>
              <li><strong>Elimination:</strong> Reach -10 points and you're out!</li>
              <li><strong>Victory:</strong> Last player standing wins!</li>
              <li><strong>Time Limit:</strong> 30 seconds per round</li>
            </ul>
          </div>
          
          <div class="lobby-status">
            <h3>🎮 Choose Game Mode</h3>

            <div class="game-mode-options">
              <div class="game-mode-card">
                <h4>🤖 Play with AI</h4>
                <p>Start immediately with 4 AI opponents</p>
                <button id="playWithAI" class="btn btn-primary btn-large">Play with AI Now</button>
              </div>

              <div class="game-mode-card">
                <h4>👥 Find Real Players</h4>
                <div class="waiting-info">
                  <div class="players-waiting">
                    <span class="count">${waitingCount}</span>
                    <span class="label">Players Waiting</span>
                  </div>
                  <div class="players-needed">
                    <span class="count">${5 - waitingCount}</span>
                    <span class="label">More Needed</span>
                  </div>
                </div>
                <button id="joinGameQueue" class="btn btn-secondary btn-large">
                  ${waitingCount === 0 ? 'Start Waiting for Players' : 'Join Queue'}
                </button>
              </div>
            </div>

            <div id="queueStatus" class="queue-status"></div>
          </div>
        </div>
      </div>
    `;
  }

  // Create active game interface
  createGameInterface(gameState) {
    const user = this.userManager.getCurrentUser();
    const currentPlayer = gameState.players.find(p => p.id === user.id);
    
    return `
      <div class="game-container">
        <header class="game-header">
          <div class="game-info">
            <h2>Round ${gameState.currentRound} 🎯</h2>
            <div class="timer-display">
              <span id="timeRemaining">30</span>s
            </div>
          </div>
          <button id="leaveGame" class="btn btn-secondary">Leave Game</button>
        </header>
        
        <div class="game-board">
          <div class="players-panel">
            <h3>Players</h3>
            <div class="players-list">
              ${gameState.players.map(player => `
                <div class="player-card ${player.isEliminated ? 'eliminated' : ''} ${player.id === user.id ? 'current-player' : ''} ${player.id.startsWith('AI_') ? 'ai-player' : ''}" data-player-id="${player.id}">
                  <div class="player-name">
                    ${player.id.startsWith('AI_') ? '🤖 ' : '👤 '}${player.username}
                  </div>
                  <div class="player-score ${player.score >= 0 ? 'positive' : 'negative'}">
                    ${player.score} pts
                  </div>
                  <div class="player-status">
                    ${player.isEliminated ? '❌ Eliminated' :
                      player.hasChosenThisRound ? '✅ Ready' : '⏳ Choosing...'}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="choice-panel">
            <h3>Choose Your Number (1-100)</h3>
            
            ${currentPlayer && !currentPlayer.isEliminated ? `
              <div class="number-input-section">
                <input type="number" id="numberChoice" min="1" max="100" 
                       placeholder="Enter 1-100" ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>
                <button id="submitChoice" class="btn btn-primary" 
                        ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>
                  ${currentPlayer.hasChosenThisRound ? 'Choice Submitted' : 'Submit Choice'}
                </button>
              </div>
              
              <div class="quick-numbers">
                <h4>Quick Select:</h4>
                <div class="number-grid">
                  ${[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map(num => `
                    <button class="quick-number ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}" 
                            data-number="${num}" ${currentPlayer.hasChosenThisRound ? 'disabled' : ''}>${num}</button>
                  `).join('')}
                </div>
              </div>
            ` : `
              <div class="eliminated-message">
                <h4>You have been eliminated</h4>
                <p>Watch the remaining players compete!</p>
              </div>
            `}
          </div>
        </div>
        
        <div id="roundResults" class="round-results"></div>
      </div>
    `;
  }

  // Create round results display
  createRoundResults(roundResult) {
    return `
      <div class="round-result-card">
        <h3>Round ${roundResult.round} Results</h3>
        
        <div class="calculation-display">
          <div class="choices">
            <h4>Player Choices:</h4>
            ${roundResult.choices.map(choice => `
              <span class="choice-item">${choice.username}: ${choice.choice}</span>
            `).join('')}
          </div>
          
          <div class="calculation">
            <div class="calc-step">Sum: ${roundResult.sum}</div>
            <div class="calc-step">Target: ${roundResult.sum} × 0.8 = ${roundResult.target.toFixed(2)}</div>
          </div>
          
          <div class="winner-announcement">
            <h4>🏆 Round Winner: ${roundResult.winner.username}</h4>
            <p>Chose ${roundResult.winner.choice} (Distance: ${roundResult.minDistance.toFixed(2)})</p>
          </div>
        </div>
        
        <div class="score-update">
          <h4>Updated Scores:</h4>
          <div class="scores-grid">
            ${roundResult.playerScores.map(player => `
              <div class="score-item ${player.isEliminated ? 'eliminated' : ''}">
                <span class="name">${player.username}</span>
                <span class="score ${player.score >= 0 ? 'positive' : 'negative'}">${player.score}</span>
                ${player.isEliminated ? '<span class="status">ELIMINATED</span>' : ''}
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  // Create game end screen
  createGameEndScreen(gameResult) {
    return `
      <div class="game-container">
        <div class="game-end-screen">
          <h2>🎉 Game Over!</h2>
          
          ${gameResult.winner ? `
            <div class="winner-celebration">
              <h3>🏆 Winner: ${gameResult.winner.username}</h3>
              <p>Congratulations! You survived ${gameResult.totalRounds} rounds!</p>
            </div>
          ` : `
            <div class="no-winner">
              <h3>Game Ended</h3>
              <p>No winner determined</p>
            </div>
          `}
          
          <div class="final-scores">
            <h3>Final Scores</h3>
            <div class="scores-table">
              ${gameResult.finalScores
                .sort((a, b) => b.score - a.score)
                .map((player, index) => `
                  <div class="score-row ${index === 0 && !player.isEliminated ? 'winner' : ''}">
                    <span class="rank">#${index + 1}</span>
                    <span class="name">${player.username}</span>
                    <span class="score ${player.score >= 0 ? 'positive' : 'negative'}">${player.score}</span>
                    <span class="status">${player.isEliminated ? 'Eliminated' : 'Survived'}</span>
                  </div>
                `).join('')}
            </div>
          </div>
          
          <div class="game-actions">
            <button id="playAgain" class="btn btn-primary">Play Again</button>
            <button id="backToDashboard" class="btn btn-secondary">Back to Dashboard</button>
          </div>
        </div>
      </div>
    `;
  }

  // Setup event listeners for game interface
  setupGameListeners() {
    // Play with AI button
    const playWithAIBtn = document.getElementById('playWithAI');
    if (playWithAIBtn) {
      playWithAIBtn.addEventListener('click', () => {
        this.startAIGame();
      });
    }

    // Join game queue
    const joinQueueBtn = document.getElementById('joinGameQueue');
    if (joinQueueBtn) {
      joinQueueBtn.addEventListener('click', () => {
        this.joinGameQueue();
      });
    }

    // Submit number choice
    const submitChoiceBtn = document.getElementById('submitChoice');
    if (submitChoiceBtn) {
      submitChoiceBtn.addEventListener('click', () => {
        this.submitChoice();
      });
    }

    // Quick number selection
    const quickNumbers = document.querySelectorAll('.quick-number');
    quickNumbers.forEach(btn => {
      btn.addEventListener('click', () => {
        if (!btn.disabled) {
          const number = parseInt(btn.dataset.number);
          document.getElementById('numberChoice').value = number;
          this.submitChoice();
        }
      });
    });

    // Number input enter key
    const numberInput = document.getElementById('numberChoice');
    if (numberInput) {
      numberInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.submitChoice();
        }
      });
    }

    // Navigation buttons
    const backBtn = document.getElementById('backToDashboard');
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        this.backToDashboard();
      });
    }

    const leaveGameBtn = document.getElementById('leaveGame');
    if (leaveGameBtn) {
      leaveGameBtn.addEventListener('click', () => {
        this.leaveGame();
      });
    }

    const playAgainBtn = document.getElementById('playAgain');
    if (playAgainBtn) {
      playAgainBtn.addEventListener('click', () => {
        this.showGameLobby();
      });
    }
  }

  // Game actions
  startAIGame() {
    // Import the MathMindGame class and create a new game with AI
    import('./game.js').then(({ MathMindGame }) => {
      this.currentGame = new MathMindGame();
      const user = this.userManager.getCurrentUser();

      // Add the current user
      this.currentGame.addPlayer(user);

      // Add 4 AI players
      this.aiPlayers.forEach(player => {
        this.currentGame.addPlayer(player);
      });

      // Start the game
      this.currentGame.startGame();
      this.showGameInterface();
      this.setupAIPlayers();
    });
  }

  joinGameQueue() {
    const user = this.userManager.getCurrentUser();
    try {
      const result = this.gameRoomManager.addPlayerToQueue(user);
      this.showMessage('queueStatus', `You are #${result.position} in queue. ${result.playersNeeded} more players needed.`, 'info');

      // Check for game creation every second
      this.checkForGameStart();
    } catch (error) {
      this.showMessage('queueStatus', error.message, 'error');
    }
  }

  checkForGameStart() {
    // This would normally be handled by a real-time system
    // For demo purposes, we'll simulate it
    setTimeout(() => {
      const gameCreated = this.gameRoomManager.tryCreateGame();
      if (gameCreated) {
        this.currentGameId = gameCreated.gameId;
        this.currentGame = gameCreated.game;
        this.startGame();
      } else {
        // Continue checking if still in queue
        const waitingCount = this.gameRoomManager.getWaitingPlayersCount();
        if (waitingCount > 0) {
          this.checkForGameStart();
        }
      }
    }, 1000);
  }

  startGame() {
    if (this.currentGame) {
      const result = this.currentGame.startGame();
      this.showGameInterface();
    }
  }

  submitChoice() {
    const numberInput = document.getElementById('numberChoice');
    const number = parseInt(numberInput.value);
    
    if (!number || number < 1 || number > 100) {
      alert('Please enter a number between 1 and 100');
      return;
    }

    try {
      const user = this.userManager.getCurrentUser();
      const result = this.currentGame.makeChoice(user.id, number);
      
      // Update UI to show choice submitted
      document.getElementById('submitChoice').disabled = true;
      document.getElementById('submitChoice').textContent = 'Choice Submitted';
      numberInput.disabled = true;
      
      // Disable quick number buttons
      document.querySelectorAll('.quick-number').forEach(btn => {
        btn.disabled = true;
      });
      
    } catch (error) {
      alert(error.message);
    }
  }

  showGameLobby() {
    document.getElementById('app').innerHTML = this.createGameLobby();
    this.setupGameListeners();
  }

  showGameInterface() {
    if (this.currentGame) {
      const gameState = this.currentGame.getGameState();
      document.getElementById('app').innerHTML = this.createGameInterface(gameState);
      this.setupGameListeners();
      this.startTimer();

      // Focus on number input for better UX
      setTimeout(() => {
        const numberInput = document.getElementById('numberChoice');
        if (numberInput && !numberInput.disabled) {
          numberInput.focus();
        }
      }, 100);
    }
  }

  setupAIPlayers() {
    // Set up AI to make automatic choices
    this.aiInterval = setInterval(() => {
      if (this.currentGame && this.currentGame.gameState === 'playing') {
        this.aiPlayers.forEach(aiPlayer => {
          const player = this.currentGame.getPlayer(aiPlayer.id);
          if (player && !player.isEliminated && !player.hasChosenThisRound) {
            // Make AI choice after random delay
            setTimeout(() => {
              try {
                const choice = this.getAIChoice(aiPlayer.id);
                this.currentGame.makeChoice(aiPlayer.id, choice);
                this.updateGameDisplay();
              } catch (error) {
                console.log(`AI ${aiPlayer.username} couldn't make choice:`, error.message);
              }
            }, Math.random() * 5000 + 1000); // 1-6 seconds delay
          }
        });
      } else if (this.currentGame && this.currentGame.gameState === 'finished') {
        clearInterval(this.aiInterval);
      }
    }, 2000); // Check every 2 seconds
  }

  getAIChoice(playerId) {
    const strategies = {
      'AI_1': () => Math.floor(Math.random() * 30) + 35, // Alice: 35-65 (middle range)
      'AI_2': () => Math.floor(Math.random() * 20) + 1,  // Bob: 1-20 (low numbers)
      'AI_3': () => Math.floor(Math.random() * 20) + 80, // Charlie: 80-100 (high numbers)
      'AI_4': () => Math.floor(Math.random() * 100) + 1  // Diana: 1-100 (random)
    };
    return strategies[playerId] ? strategies[playerId]() : Math.floor(Math.random() * 100) + 1;
  }

  startTimer() {
    this.remainingTime = 30;
    this.updateTimerDisplay();

    this.timerInterval = setInterval(() => {
      this.remainingTime--;
      this.updateTimerDisplay();

      if (this.remainingTime <= 0) {
        clearInterval(this.timerInterval);
      }
    }, 1000);
  }

  updateTimerDisplay() {
    const timerEl = document.getElementById('timeRemaining');
    if (timerEl) {
      timerEl.textContent = this.remainingTime;
      timerEl.className = this.remainingTime <= 10 ? 'urgent' : '';
    }
  }

  updateGameDisplay() {
    // Refresh the game interface with current state
    if (this.currentGame) {
      const gameState = this.currentGame.getGameState();

      // Update player status
      gameState.players.forEach(player => {
        const playerCard = document.querySelector(`[data-player-id="${player.id}"]`);
        if (playerCard) {
          const statusEl = playerCard.querySelector('.player-status');
          if (statusEl) {
            statusEl.textContent = player.isEliminated ? '❌ Eliminated' :
                                  player.hasChosenThisRound ? '✅ Ready' : '⏳ Choosing...';
          }
        }
      });

      // Check if round is complete and show results
      const lastResult = this.currentGame.gameHistory[this.currentGame.gameHistory.length - 1];
      if (lastResult && lastResult.round === this.currentGame.currentRound - 1) {
        this.showRoundResult(lastResult);
      }

      // Check if game is finished
      if (this.currentGame.gameState === 'finished') {
        setTimeout(() => {
          this.showGameEnd();
        }, 3000);
      }
    }
  }

  leaveGame() {
    if (this.currentGame && this.currentGameId) {
      const user = this.userManager.getCurrentUser();
      this.currentGame.removePlayer(user.id);
      this.gameRoomManager.removeGame(this.currentGameId);
    }
    this.backToDashboard();
  }

  showRoundResult(roundResult) {
    const resultsDiv = document.getElementById('roundResults');
    if (resultsDiv) {
      resultsDiv.innerHTML = this.createRoundResults(roundResult);
    }
  }

  createRoundResults(roundResult) {
    return `
      <div class="round-result-card">
        <h3>Round ${roundResult.round} Results</h3>

        <div class="calculation-display">
          <div class="choices">
            <h4>Player Choices:</h4>
            ${roundResult.choices.map(choice => `
              <span class="choice-item">${choice.username}: ${choice.choice}</span>
            `).join('')}
          </div>

          <div class="calculation">
            <div class="calc-step">Sum: ${roundResult.sum}</div>
            <div class="calc-step">Target: ${roundResult.sum} × 0.8 = ${roundResult.target.toFixed(2)}</div>
          </div>

          <div class="winner-announcement">
            <h4>🏆 Round Winner: ${roundResult.winner.username}</h4>
            <p>Chose ${roundResult.winner.choice} (Distance: ${roundResult.minDistance.toFixed(2)})</p>
          </div>
        </div>
      </div>
    `;
  }

  showGameEnd() {
    if (this.aiInterval) {
      clearInterval(this.aiInterval);
    }
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    const gameResult = this.currentGame.endGame();
    document.getElementById('app').innerHTML = this.createGameEndScreen(gameResult);
    this.setupGameListeners();
  }

  backToDashboard() {
    // Clear any timers
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    if (this.aiInterval) {
      clearInterval(this.aiInterval);
    }

    // Reset game state
    this.currentGame = null;
    this.currentGameId = null;

    // Return to dashboard (this would be handled by the main UI controller)
    window.location.reload();
  }

  showMessage(elementId, message, type = 'info') {
    const messageEl = document.getElementById(elementId);
    if (messageEl) {
      messageEl.textContent = message;
      messageEl.className = `message ${type}`;
    }
  }
}
